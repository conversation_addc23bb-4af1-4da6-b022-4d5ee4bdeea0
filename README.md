# ICU‑Lipreading MVP (Lightweight, Commercial‑Safe)

A Python 3.9+ backend for ICU lipreading that **does not use LipNet**.
It implements a **lightweight, visual‑only phrase classifier** (26 phrases) you own end‑to‑end (code + weights), and serves predictions via FastAPI/Flask.

* **No GRID, no research‑only weights.**
* **Direct phrase classification (softmax over 26 classes).**
* **Demographics evaluation** (age, gender, ethnicity) alongside overall metrics.
* **Feature‑flagged** so you can flip between legacy `/predict` and the new `/predict_v2` without touching old code.

---

## Why this change (in 15 seconds)

* LipNet's licence / pretrained weights are research‑only → not safe commercially.
* Your product needs **fixed vocabulary** (26 ICU phrases), not open‑ended lipreading.
* A **small 3D‑CNN + BiGRU** trained only on your dataset is faster, simpler, and typically **more accurate** for this task.

---

## Repo layout (new bits only)

```
backend/
  api/
    app.py                 # legacy endpoints stay; add /predict_v2 (new)
  lightweight_vsr/         # NEW: isolated module (no legacy deps)
    dataset.py
    model.py
    train.py
    infer.py
    utils_video.py
    metrics.py
    requirements.txt
    README.md
configs/
  phrases26.yaml           # class list + model dims + threshold
artifacts/                 # checkpoints, exports (gitignored)
scripts/
  run_train.sh
  export_torchscript.sh
  benchmark_infer.sh
```

> Everything in `backend/lightweight_vsr/` is **new and standalone**. You can keep your legacy code untouched and switch with an env flag.

---

## Quick start — **10 steps to ship by tomorrow**

1. **Create feature branch**

   ```bash
   git checkout -b feat/lightweight-vsr
   ```

2. **Set up a clean env**

   ```bash
   python -m venv .venv_vsr
   source .venv_vsr/bin/activate
   pip install -r backend/lightweight_vsr/requirements.txt
   ```

3. **Prepare a manifest CSV** (one row per video)

   ```
   data/manifest.csv
   video_path,speaker_id,phrase,age_group,gender,ethnicity,lighting
   /abs/path/vid001.mp4,spk001,i'm in pain,65-80,F,Aboriginal,indoor_bright
   ...
   ```

4. **Fill labels/config**

   ```yaml
   # configs/phrases26.yaml
   phrases:
     - "i'm in pain"
     - "i can't breathe"
     # ... 26 total, fixed order
   frames: 32
   height: 96
   width: 96
   grayscale: true
   confidence_threshold: 0.6
   ```

5. **Train (speaker‑wise split; class‑weighted loss)**

   ```bash
   python backend/lightweight_vsr/train.py \
     --manifest data/manifest.csv \
     --out_dir artifacts/vsr_26p_v1 \
     --config configs/phrases26.yaml \
     --epochs 40 --batch_size 16 --val_split 0.1 --test_split 0.1
   ```

6. **Target acceptance (stop if not met)**

   * **Val macro‑F1 ≥ 0.90**
   * **Worst‑phrase F1 ≥ 0.80**
   * **No demographic subgroup < (overall − 10 pts)**

7. **Export TorchScript**

   ```bash
   bash scripts/export_torchscript.sh
   # creates artifacts/vsr_26p_v1/model.ts
   ```

8. **Local inference smoke test**

   ```bash
   python -c "from backend.lightweight_vsr.infer import predict_phrase; \
              print(predict_phrase('test_webm_videos/sample.mp4'))"
   ```

9. **Serve API (new endpoint)**
   Add `/predict_v2` in `backend/api/app.py` to call `lightweight_vsr.infer.predict_phrase`, then:

   ```bash
   uvicorn backend.api.app:app --reload
   # POST /predict_v2 with a mouth video
   ```

10. **Flip switch (optional)**
    Route legacy `/predict` to new model only when ready:

```bash
export VSR_IMPL=lightweight   # default stays legacy if unset
```

Rollback: `unset VSR_IMPL` and restart.

---

## Data & preprocessing

* **Expected input**: short MP4/WebM mouth‑focused videos.
* **Frame pipeline**:

  * Extract **32 frames** around speech (trim/pad as needed).
  * Convert to **grayscale**; **resize to 96×96**.
  * Keep some nose/chin context; add **vertical jitter** (±8 px) to handle "mouth near top".
  * Augment: brightness/contrast ±15%, scale ±10%, slight temporal jitter (±4 frames).
* **Splits**: **speaker‑wise** 80/10/10 (no speaker leakage).
* **Imbalance**: automatic class weights; optional focal loss.

**Folder option (if you don't use a manifest):**

```
data/
  i'm in pain/
    spk001_clip01.mp4
    ...
  i can't breathe/
    ...
```

(Manifest is strongly preferred so you can track demographics and lighting.)

---

## Model (Mobile3D‑Tiny + BiGRU)

* **Backbone**: Depthwise‑separable 3D conv blocks (≤ \~8M params).
* **Temporal**: Spatial GAP → **BiGRU(256×2)** → mean over time.
* **Head**: LayerNorm → Dropout(0.2) → Linear(26) → Softmax.
* **Loss**: Weighted cross‑entropy (or focal).
* **Optim**: AdamW (lr=1e‑3, wd=1e‑2), cosine decay, warmup 1–2 epochs.
* **AMP**: Mixed precision on by default.
