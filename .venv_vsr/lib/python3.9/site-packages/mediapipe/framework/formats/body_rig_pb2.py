# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/formats/body_rig.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n*mediapipe/framework/formats/body_rig.proto\x12\tmediapipe\"0\n\x05Joint\x12\x13\n\x0brotation_6d\x18\x01 \x03(\x02\x12\x12\n\nvisibility\x18\x02 \x01(\x02\",\n\tJointList\x12\x1f\n\x05joint\x18\x01 \x03(\x0b\x32\x10.mediapipe.Joint')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.formats.body_rig_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_JOINT']._serialized_start=57
  _globals['_JOINT']._serialized_end=105
  _globals['_JOINTLIST']._serialized_start=107
  _globals['_JOINTLIST']._serialized_end=151
# @@protoc_insertion_point(module_scope)
