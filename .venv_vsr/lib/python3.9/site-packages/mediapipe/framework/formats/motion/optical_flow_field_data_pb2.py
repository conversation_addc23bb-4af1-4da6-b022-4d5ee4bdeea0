# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/framework/formats/motion/optical_flow_field_data.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n@mediapipe/framework/formats/motion/optical_flow_field_data.proto\x12\tmediapipe\"U\n\x14OpticalFlowFieldData\x12\r\n\x05width\x18\x01 \x01(\x05\x12\x0e\n\x06height\x18\x02 \x01(\x05\x12\x0e\n\x02\x64x\x18\x03 \x03(\x02\x42\x02\x10\x01\x12\x0e\n\x02\x64y\x18\x04 \x03(\x02\x42\x02\x10\x01')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.framework.formats.motion.optical_flow_field_data_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_OPTICALFLOWFIELDDATA'].fields_by_name['dx']._options = None
  _globals['_OPTICALFLOWFIELDDATA'].fields_by_name['dx']._serialized_options = b'\020\001'
  _globals['_OPTICALFLOWFIELDDATA'].fields_by_name['dy']._options = None
  _globals['_OPTICALFLOWFIELDDATA'].fields_by_name['dy']._serialized_options = b'\020\001'
  _globals['_OPTICALFLOWFIELDDATA']._serialized_start=79
  _globals['_OPTICALFLOWFIELDDATA']._serialized_end=164
# @@protoc_insertion_point(module_scope)
