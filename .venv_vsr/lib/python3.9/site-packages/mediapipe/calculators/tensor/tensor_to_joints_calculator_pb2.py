# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mediapipe/calculators/tensor/tensor_to_joints_calculator.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from mediapipe.framework import calculator_pb2 as mediapipe_dot_framework_dot_calculator__pb2
try:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe_dot_framework_dot_calculator__options__pb2
except AttributeError:
  mediapipe_dot_framework_dot_calculator__options__pb2 = mediapipe_dot_framework_dot_calculator__pb2.mediapipe.framework.calculator_options_pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n>mediapipe/calculators/tensor/tensor_to_joints_calculator.proto\x12\tmediapipe\x1a$mediapipe/framework/calculator.proto\"\xa8\x01\n\x1fTensorToJointsCalculatorOptions\x12\x12\n\nnum_joints\x18\x01 \x01(\x05\x12\x16\n\x0bstart_index\x18\x02 \x01(\x05:\x01\x30\x32Y\n\x03\x65xt\x12\x1c.mediapipe.CalculatorOptions\x18\xf1\x91\xe7\xc1\x01 \x01(\x0b\x32*.mediapipe.TensorToJointsCalculatorOptions')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'mediapipe.calculators.tensor.tensor_to_joints_calculator_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_TENSORTOJOINTSCALCULATOROPTIONS']._serialized_start=116
  _globals['_TENSORTOJOINTSCALCULATOROPTIONS']._serialized_end=284
# @@protoc_insertion_point(module_scope)
