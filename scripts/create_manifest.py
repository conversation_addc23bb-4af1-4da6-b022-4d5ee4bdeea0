#!/usr/bin/env python3
"""
Create manifest CSV from existing data folder structure
"""

import pandas as pd
from pathlib import Path
import argparse
import yaml


def create_manifest_from_folders(data_dir: str, config_path: str, output_path: str):
    """Create manifest CSV from folder structure"""
    
    # Load config to get phrase list
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    valid_phrases = config['phrases']
    data_path = Path(data_dir)
    
    records = []
    
    for phrase in valid_phrases:
        # Convert phrase to folder name (replace spaces with underscores)
        folder_name = phrase.replace(' ', '_')
        phrase_dir = data_path / folder_name
        
        if not phrase_dir.exists():
            print(f"Warning: Phrase directory not found: {phrase_dir}")
            continue
        
        print(f"Processing {phrase_dir}...")
        
        # Find video files
        video_extensions = ['.mp4', '.webm', '.avi', '.mov']
        video_count = 0
        
        for ext in video_extensions:
            for video_file in phrase_dir.glob(f'*{ext}'):
                # Extract metadata from filename
                filename = video_file.stem
                
                # Try to extract speaker ID
                if '_' in filename:
                    parts = filename.split('_')
                    speaker_id = parts[0] if parts[0] else 'unknown'
                else:
                    speaker_id = 'unknown'
                
                # Determine demographics from filename patterns
                age_group = 'unknown'
                gender = 'unknown'
                ethnicity = 'unknown'
                lighting = 'unknown'
                
                # Try to extract demographics from filename
                filename_lower = filename.lower()
                
                # Age group detection
                if 'henry' in filename_lower:
                    age_group = '18-39'
                    gender = 'male'
                    ethnicity = 'not_specified'
                elif any(x in filename_lower for x in ['user', 'useruser']):
                    age_group = '18-39'
                    gender = 'male'
                    ethnicity = 'not_specified'
                
                # Lighting detection
                if 'bright' in filename_lower:
                    lighting = 'indoor_bright'
                elif 'dim' in filename_lower:
                    lighting = 'indoor_dim'
                else:
                    lighting = 'indoor_bright'  # Default assumption
                
                records.append({
                    'video_path': str(video_file.absolute()),
                    'speaker_id': speaker_id,
                    'phrase': phrase,
                    'age_group': age_group,
                    'gender': gender,
                    'ethnicity': ethnicity,
                    'lighting': lighting
                })
                
                video_count += 1
        
        print(f"  Found {video_count} videos")
    
    # Create DataFrame
    df = pd.DataFrame(records)
    
    if df.empty:
        print("No videos found!")
        return
    
    # Save manifest
    df.to_csv(output_path, index=False)
    
    # Print summary
    print(f"\nManifest created: {output_path}")
    print(f"Total videos: {len(df)}")
    print(f"Unique phrases: {df['phrase'].nunique()}")
    print(f"Unique speakers: {df['speaker_id'].nunique()}")
    
    print("\nPhrase distribution:")
    phrase_counts = df['phrase'].value_counts()
    for phrase, count in phrase_counts.items():
        print(f"  {phrase}: {count}")
    
    print("\nSpeaker distribution:")
    speaker_counts = df['speaker_id'].value_counts()
    for speaker, count in speaker_counts.head(10).items():
        print(f"  {speaker}: {count}")
    if len(speaker_counts) > 10:
        print(f"  ... and {len(speaker_counts) - 10} more speakers")


def main():
    parser = argparse.ArgumentParser(description='Create manifest CSV from data folders')
    parser.add_argument('--data_dir', type=str, default='data', 
                       help='Data directory containing phrase folders')
    parser.add_argument('--config', type=str, default='configs/phrases26.yaml',
                       help='Config file with phrase list')
    parser.add_argument('--output', type=str, default='data/manifest.csv',
                       help='Output manifest CSV path')
    
    args = parser.parse_args()
    
    create_manifest_from_folders(args.data_dir, args.config, args.output)


if __name__ == '__main__':
    main()
