"""
Training pipeline for lightweight VSR phrase classifier
Supports speaker-wise splits, class weights, and demographics evaluation
"""

import argparse
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
from pathlib import Path
import json
import time
from tqdm import tqdm
import numpy as np

from .model import Mobile3DTiny
from .dataset import create_dataloaders
from .metrics import (
    compute_metrics, compute_demographics_metrics, 
    save_metrics, print_metrics_summary, check_acceptance_criteria,
    evaluate_model
)


class FocalLoss(nn.Module):
    """Focal Loss for handling class imbalance"""
    
    def __init__(self, alpha=1, gamma=2, weight=None):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.weight = weight
        
    def forward(self, inputs, targets):
        ce_loss = nn.functional.cross_entropy(inputs, targets, weight=self.weight, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1-pt)**self.gamma * ce_loss
        return focal_loss.mean()


class Trainer:
    """Training manager for lightweight VSR"""
    
    def __init__(self, config: dict, output_dir: str):
        self.config = config
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Device setup
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")
        
        # Create dataloaders
        self.train_loader, self.val_loader, self.test_loader, self.data_info = create_dataloaders(
            config, 
            manifest_path=config.get('manifest_path'),
            data_dir=config.get('data_dir')
        )
        
        print(f"Dataset sizes: train={self.data_info['train_size']}, "
              f"val={self.data_info['val_size']}, test={self.data_info['test_size']}")
        
        # Create model
        self.model = Mobile3DTiny(
            num_classes=self.data_info['num_classes'],
            hidden_dim=config.get('model', {}).get('hidden_dim', 256),
            num_gru_layers=config.get('model', {}).get('num_gru_layers', 2),
            dropout=config.get('model', {}).get('dropout', 0.2)
        ).to(self.device)
        
        print(f"Model parameters: {self.model.get_num_parameters():,}")
        
        # Loss function
        loss_config = config.get('loss', {})
        if loss_config.get('type') == 'focal':
            self.criterion = FocalLoss(
                alpha=loss_config.get('alpha', 1),
                gamma=loss_config.get('gamma', 2),
                weight=self.data_info['class_weights'].to(self.device)
            )
        else:
            self.criterion = nn.CrossEntropyLoss(
                weight=self.data_info['class_weights'].to(self.device)
            )
        
        # Optimizer
        optimizer_config = config.get('optimizer', {})
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=optimizer_config.get('lr', 0.001),
            weight_decay=optimizer_config.get('weight_decay', 0.01)
        )
        
        # Scheduler
        scheduler_config = config.get('scheduler', {})
        if scheduler_config.get('type') == 'cosine':
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer, 
                T_max=config.get('epochs', 40),
                eta_min=1e-6
            )
            
            # Warmup scheduler
            warmup_epochs = scheduler_config.get('warmup_epochs', 2)
            if warmup_epochs > 0:
                self.warmup_scheduler = optim.lr_scheduler.LinearLR(
                    self.optimizer,
                    start_factor=0.1,
                    total_iters=warmup_epochs
                )
            else:
                self.warmup_scheduler = None
        else:
            self.scheduler = None
            self.warmup_scheduler = None
        
        # Mixed precision
        self.use_amp = config.get('amp', True)
        self.scaler = torch.cuda.amp.GradScaler() if self.use_amp else None
        
        # Tensorboard
        self.writer = SummaryWriter(self.output_dir / 'tensorboard')
        
        # Training state
        self.best_val_f1 = 0.0
        self.epoch = 0
        
    def train_epoch(self) -> dict:
        """Train for one epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        pbar = tqdm(self.train_loader, desc=f'Epoch {self.epoch+1} Train')
        
        for batch_videos, batch_labels, _ in pbar:
            batch_videos = batch_videos.to(self.device)
            batch_labels = batch_labels.to(self.device)
            
            self.optimizer.zero_grad()
            
            # Forward pass with mixed precision
            if self.use_amp:
                with torch.cuda.amp.autocast():
                    logits = self.model(batch_videos)
                    loss = self.criterion(logits, batch_labels)
                
                self.scaler.scale(loss).backward()
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                logits = self.model(batch_videos)
                loss = self.criterion(logits, batch_labels)
                loss.backward()
                self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            pbar.set_postfix({'loss': f'{loss.item():.4f}'})
        
        avg_loss = total_loss / num_batches
        return {'loss': avg_loss}
    
    def validate(self) -> dict:
        """Validate model"""
        phrase_names = [self.data_info['idx_to_phrase'][i] for i in range(self.data_info['num_classes'])]
        
        y_true, y_pred, metadata = evaluate_model(
            self.model, self.val_loader, self.device, phrase_names
        )
        
        # Compute metrics
        metrics = compute_demographics_metrics(y_true, y_pred, metadata, phrase_names)
        
        return metrics
    
    def test(self) -> dict:
        """Test model on test set"""
        phrase_names = [self.data_info['idx_to_phrase'][i] for i in range(self.data_info['num_classes'])]
        
        y_true, y_pred, metadata = evaluate_model(
            self.model, self.test_loader, self.device, phrase_names
        )
        
        # Compute metrics
        metrics = compute_demographics_metrics(y_true, y_pred, metadata, phrase_names)
        
        return metrics
    
    def save_checkpoint(self, is_best: bool = False):
        """Save model checkpoint"""
        checkpoint = {
            'epoch': self.epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'best_val_f1': self.best_val_f1,
            'config': self.config,
            'data_info': self.data_info
        }
        
        if self.scheduler:
            checkpoint['scheduler_state_dict'] = self.scheduler.state_dict()
        
        # Save last checkpoint
        torch.save(checkpoint, self.output_dir / 'last.ckpt')
        
        # Save best checkpoint
        if is_best:
            torch.save(checkpoint, self.output_dir / 'best.ckpt')
            print(f"New best model saved with val F1: {self.best_val_f1:.4f}")
    
    def train(self, epochs: int):
        """Main training loop"""
        print(f"Starting training for {epochs} epochs...")
        
        for epoch in range(epochs):
            self.epoch = epoch
            start_time = time.time()
            
            # Learning rate warmup
            if self.warmup_scheduler and epoch < self.config.get('scheduler', {}).get('warmup_epochs', 2):
                current_lr = self.warmup_scheduler.get_last_lr()[0]
            elif self.scheduler:
                current_lr = self.scheduler.get_last_lr()[0]
            else:
                current_lr = self.optimizer.param_groups[0]['lr']
            
            # Train
            train_metrics = self.train_epoch()
            
            # Validate
            val_metrics = self.validate()
            val_f1 = val_metrics['overall']['macro_f1']
            
            # Update learning rate
            if self.warmup_scheduler and epoch < self.config.get('scheduler', {}).get('warmup_epochs', 2):
                self.warmup_scheduler.step()
            elif self.scheduler:
                self.scheduler.step()
            
            # Log metrics
            epoch_time = time.time() - start_time
            
            self.writer.add_scalar('Train/Loss', train_metrics['loss'], epoch)
            self.writer.add_scalar('Val/MacroF1', val_f1, epoch)
            self.writer.add_scalar('Val/Accuracy', val_metrics['overall']['accuracy'], epoch)
            self.writer.add_scalar('Learning_Rate', current_lr, epoch)
            
            print(f"Epoch {epoch+1}/{epochs} ({epoch_time:.1f}s) - "
                  f"Train Loss: {train_metrics['loss']:.4f}, "
                  f"Val F1: {val_f1:.4f}, LR: {current_lr:.2e}")
            
            # Save checkpoint
            is_best = val_f1 > self.best_val_f1
            if is_best:
                self.best_val_f1 = val_f1
            
            self.save_checkpoint(is_best)
            
            # Early stopping check
            if val_f1 >= 0.90:
                print(f"Reached target F1 of 0.90 at epoch {epoch+1}")
                break
        
        # Final test evaluation
        print("\nEvaluating on test set...")
        test_metrics = self.test()
        
        # Save final metrics
        save_metrics(val_metrics, self.output_dir / 'metrics.json')
        save_metrics(test_metrics, self.output_dir / 'test_metrics.json')
        
        # Print final results
        print_metrics_summary(test_metrics)
        
        # Check acceptance criteria
        passed, issues = check_acceptance_criteria(test_metrics)
        print(f"\nAcceptance criteria: {'✅ PASSED' if passed else '❌ FAILED'}")
        if issues:
            for issue in issues:
                print(f"  - {issue}")
        
        self.writer.close()
        
        return test_metrics


def load_config(config_path: str) -> dict:
    """Load configuration from YAML file"""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def main():
    parser = argparse.ArgumentParser(description='Train lightweight VSR model')
    parser.add_argument('--manifest', type=str, help='Path to manifest CSV file')
    parser.add_argument('--data_dir', type=str, help='Path to data directory')
    parser.add_argument('--config', type=str, required=True, help='Path to config YAML file')
    parser.add_argument('--out_dir', type=str, required=True, help='Output directory')
    parser.add_argument('--epochs', type=int, default=40, help='Number of epochs')
    parser.add_argument('--batch_size', type=int, help='Batch size (overrides config)')
    parser.add_argument('--val_split', type=float, help='Validation split (overrides config)')
    parser.add_argument('--test_split', type=float, help='Test split (overrides config)')
    parser.add_argument('--num_workers', type=int, help='Number of data loader workers')
    parser.add_argument('--amp', type=int, choices=[0, 1], help='Use mixed precision (0/1)')
    
    args = parser.parse_args()
    
    # Load config
    config = load_config(args.config)
    
    # Override config with command line arguments
    if args.manifest:
        config['manifest_path'] = args.manifest
    if args.data_dir:
        config['data_dir'] = args.data_dir
    if args.batch_size:
        config['batch_size'] = args.batch_size
    if args.val_split:
        config['val_split'] = args.val_split
    if args.test_split:
        config['test_split'] = args.test_split
    if args.num_workers:
        config['num_workers'] = args.num_workers
    if args.amp is not None:
        config['amp'] = bool(args.amp)
    
    config['epochs'] = args.epochs
    
    # Create trainer and train
    trainer = Trainer(config, args.out_dir)
    trainer.train(args.epochs)


if __name__ == '__main__':
    main()
